
<div class="p-4 border rounded-lg bg-white shadow-sm text-sm">
    
    <div class="mb-4">
        <h2 class="text-xl font-bold"><?php echo e($sampleData['sample_name'] ?? $companyData['name'] ?? 'Company Name'); ?></h2>
        <p class="text-gray-600">#INV-0001 | <?php echo e(now()->format('d F Y')); ?></p>
    </div>

    
    <!--[if BLOCK]><![endif]--><?php if($companyData['address'] || $companyData['phone'] || $companyData['email']): ?>
    <div class="mb-4 p-3 bg-gray-50 rounded">
        <h3 class="font-semibold text-gray-800 mb-2">Company Information</h3>
        <!--[if BLOCK]><![endif]--><?php if($companyData['address']): ?>
            <p class="text-gray-700">Address: <?php echo strip_tags($companyData['address']); ?></p>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <!--[if BLOCK]><![endif]--><?php if($companyData['phone']): ?>
            <p class="text-gray-700">Phone: <?php echo e($companyData['phone']); ?></p>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <!--[if BLOCK]><![endif]--><?php if($companyData['email']): ?>
            <p class="text-gray-700">Email: <?php echo e($companyData['email']); ?></p>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <div class="mb-4">
        <p class="text-gray-700">Customer Name: John Doe</p>
        <p class="text-gray-700">Address: Jl. Contoh No. 123, Jakarta</p>
    </div>

    
    <div class="overflow-x-auto mb-4">
        <table class="w-full border border-gray-300">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1 text-left">Item</th>
                    <th class="border px-2 py-1 text-right">Qty</th>
                    <th class="border px-2 py-1 text-right">Price</th>
                    <th class="border px-2 py-1 text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border px-2 py-1">Product A</td>
                    <td class="border px-2 py-1 text-right">2</td>
                    <td class="border px-2 py-1 text-right">100,000</td>
                    <td class="border px-2 py-1 text-right">200,000</td>
                </tr>
                <tr>
                    <td class="border px-2 py-1">Product B</td>
                    <td class="border px-2 py-1 text-right">1</td>
                    <td class="border px-2 py-1 text-right">50,000</td>
                    <td class="border px-2 py-1 text-right">50,000</td>
                </tr>
            </tbody>
            <tfoot class="bg-gray-50 font-semibold">
                <tr>
                    <td colspan="3" class="border px-2 py-1 text-right">Subtotal</td>
                    <td class="border px-2 py-1 text-right">250,000</td>
                </tr>
                <tr>
                    <td colspan="3" class="border px-2 py-1 text-right">Tax (10%)</td>
                    <td class="border px-2 py-1 text-right">25,000</td>
                </tr>
                <tr>
                    <td colspan="3" class="border px-2 py-1 text-right">Total</td>
                    <td class="border px-2 py-1 text-right">275,000</td>
                </tr>
            </tfoot>
        </table>
    </div>

    
    <div class="mt-6 border-t pt-4">
        <p class="text-gray-700">Bank: BCA</p>
        <p class="text-gray-700">Account No: *********</p>
        <p class="text-gray-700 mt-2">Authorized Signature: ____________</p>
    </div>

</div>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/filament/admin/resources/invoice/preview.blade.php ENDPATH**/ ?>