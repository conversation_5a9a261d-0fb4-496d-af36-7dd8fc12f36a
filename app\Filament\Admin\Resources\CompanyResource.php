<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CompanyResource\Pages;
use App\Filament\Admin\Resources\CompanyResource\RelationManagers;
use App\Filament\Admin\Resources\CompanyResource\RelationManagers\BanksRelationManager;
use App\Models\BusinessType;
use App\Models\Company;
use App\Models\Font;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\{Actions, CheckboxList, ColorPicker, Fieldset, FileUpload, Group, Hidden, Placeholder, Radio, Repeater, RichEditor, Select, Textarea, TextInput, Toggle, View};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Symfony\Component\Yaml\Inline;
use thiagoalessio\TesseractOCR\TesseractOCR;

class CompanyResource extends Resource
{
    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
	protected static ?string $activeNavigationIcon = 'heroicon-s-building-office-2';
	protected static ?string $navigationGroup = 'Masters Data';
	protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
			->columns(7)
            ->schema([
				Group::make()
					->columnSpan(3)
					->statePath('sample_data')
					->schema([
						TextInput::make('sample_name')
							->required()
							->label('Company Name')
							->inlineLabel(),
					]),
				Section::make('Invoice Preview')
					->columnSpan(4)
					->schema([
						View::make('filament.admin.resources.invoice.preview')
							->reactive()
							->viewData([
								'sampleName' => fn () => data_get(
									Filament\Forms\get_form_component_state(),
									'sample_data.sample_name'
								),
							])
							->columnSpanFull(),
					]),
				Group::make()
					// ->hidden()
					->schema([
						Section::make('Basic Information')
							->columnSpanFull()
							->columns(12)
							->schema([
								Group::make()
									->columnSpanFull()
									->extraAttributes(['class' => 'mb-6'])
									->schema([
										Toggle::make('showOcr')
											// ->inlineLabel()
											->inline()
											->reactive()
											->label('Enable OCR'),
										FileUpload::make('ocr')
											->reactive()
											->visible(fn ($get) => $get('showOcr'))
											->label('OCR')
											->image()
											->directory('ocr-temp')
											->multiple(false)
											->disk('local')
											->preserveFilenames()
											->afterStateUpdated(function ($state, callable $set) {
												if (!$state) {
													$set('ocr_text', '');
													return;
												}

												// 🛠️ Gunakan path asli dari uploaded file
												if ($state instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
													$tmpPath = $state->getRealPath();
												} elseif (is_string($state)) {
													$tmpPath = storage_path('app/' . $state);
												} else {
													$set('ocr_text', '[File tidak dikenali]');
													return;
												}

												if (!file_exists($tmpPath)) {
													$set('ocr_text', '[File belum tersedia]');
													return;
												}

												// Salin file ke penyimpanan permanen
												$filename = uniqid('ocr_') . '.' . pathinfo($tmpPath, PATHINFO_EXTENSION);
												$newPath = 'ocr-temp/' . $filename;
												Storage::disk('local')->put($newPath, file_get_contents($tmpPath));
												$ocrPath = Storage::disk('local')->path($newPath);

												// Jalankan OCR
												$text = (new TesseractOCR($ocrPath))
													->lang('eng')
													->run();

												// Set hasil OCR ke field lain
												$set('ocr_text', $text);

												// 🟡 Jangan timpa $set('ocr')! Itu menyebabkan state konflik.
												// Simpan ke hidden field jika perlu
												$set('ocr_final_path', $newPath);
											})
											->hintAction(
												Action::make('reset_ocr')
													->label('Reset OCR')
													->color('danger')
													->icon('heroicon-o-trash')
													->action(function ($get, $set) {
														$ocrPath = $get('ocr');
														if ($ocrPath && is_string($ocrPath) && Storage::disk('local')->exists($ocrPath)) {
															Storage::disk('local')->delete($ocrPath);
														}

														$set('ocr', null);
														$set('ocr_text', null);
													}),
											),
										Hidden::make('ocr_final_path'),
										Textarea::make('ocr_text')
											->label('OCR Output')
											->rows(10)
											->visible(fn ($get) => $get('showOcr'))
											->columnSpanFull()
											->reactive()
											->helperText(function ($state) {
												return blank($state)
													? null
													: new HtmlString('<span class="text-danger-500 text-sm">Please check the OCR output before paste them to the appropriate fields.</span>');
											})
											->placeholder(function ($state) {
												return blank($state)
													? 'You can copy the OCR output and paste them to the appropriate fields.'
													: null;
											}),
									]),
								Group::make()
									->columnSpan(8)
									->schema([
										TextInput::make('name')
											->required()
											->label('Company Name')
											->reactive()
											->inlineLabel(),
										Select::make('business_type')
											->inlineLabel()
											->required()
											->searchable()
											->options(fn() => BusinessType::get()->pluck('name', 'id')),
										RichEditor::make('address')
											->extraInputAttributes([
												'style' => 'resize: vertical;min-height: 2rem; max-height: auto; overflow-y: auto;'
												])
											->inlineLabel()
											->helperText('Shift+Enter to add new row.')
											->toolbarButtons([]),

										TextInput::make('signature_name')
											->helperText('Needed when Invoices has Signature name')
											->inlineLabel(),

										Radio::make('template_version')
											->inline()
											->columns(2)
											->inlineLabel()
											->default('v1')
											->reactive()
											// ->visible(fn ($record) => $record?->template !== null)
											->options([
												'v1' => 'Legacy',
												'v2' => 'Custom',
											])
											->descriptions([
												'v1' => 'Use legacy/old method to build the invoice layout.',
												'v2' => 'Use new custom invoice builder method to build the invoice layout.'
											]),
									]),
								Group::make()
									->columnSpan(4)
									->columns(2)
									->schema([
										TextInput::make('phone')
											->tel()
											->columnSpanFull()
											->inlineLabel(),
										TextInput::make('email')
											->email()
											->columnSpanFull()
											->inlineLabel(),
										Select::make('type')
											->required()
											->reactive()
											->columnSpanFull()
											->inlineLabel()
											->options([
												1 => 'Client',
												2 => 'Internal'
											]),

										FileUpload::make('logo')
											->image()
											->columnSpan(1)
											->disk('public')
											->label('Company Logo')
											->panelAspectRatio('1:1')
											->panelLayout('integrated')
											->imagePreviewHeight('50')
											->fetchFileInformation(false)
											->directory('logo')
											->loadingIndicatorPosition('left'),
										FileUpload::make('signature')
											->image()
											->columnSpan(1)
											->disk('public')
											->label('Signature')
											->panelAspectRatio('1:1')
											->panelLayout('integrated')
											->imagePreviewHeight('50')
											->fetchFileInformation(false)
											->directory('signature')
											->loadingIndicatorPosition('left')
											->helperText('Needed when Invoices has Signature.'),

									]),
							]),

						Section::make('Invoice Template Builder')
							->columnSpanFull()
							->collapsible()
							->visible(fn ($get) => $get('template_version') === 'v2')
							->relationship('invoicetemplate')

							// ->aside()
							// ->collapsed()
							->schema([
								Group::make()
									->schema([
										Select::make('font_id')
											->searchable()
											->label('Global Font')
											->inlineLabel()
											->relationship('font')
											->placeholder('Select Font')
											->helperText('Please visit https://fonts.google.com to get more font')
											->createOptionForm([
												TextInput::make('name')->inlineLabel()->columnSpanFull()->required(),
												TextInput::make('source')
													->label('Source URL')
													->inlineLabel()
													->columnSpanFull()
													->rules(['nullable', 'url'])
													->required(),
											])
											->options(
												Font::get()
													->mapWithKeys(fn ($font) => [
														$font->id => "{$font->name}"
													])
													->toArray()
											),

										Group::make()
											->statePath('layout_config')
											->schema([
												Radio::make('paper_size')
													->inlineLabel()
													->inline()
													->options([
														'legal' => 'Legal',
														'f4' => 'F4',
														'a4' => 'A4',
													])
													->descriptions([
														'legal' => 'size: 216mm x  356mm',
														'f4' => 'size: 210mm x  330mm (default)',
														'a4' => 'size: 210mm x  297mm',
													]),
												Group::make()
													->columns(3)
													->schema([
														Placeholder::make('Margin (mm)')->inlineLabel(),
														Group::make()
															->columnSpan(2)
															->columns(4)
															->schema([
																TextInput::make('margin_top')->label('Top')->integer()->inlineLabel(),
																TextInput::make('margin_left')->label('Left')->integer()->inlineLabel(),
																TextInput::make('margin_bottom')->label('Bottom')->integer()->inlineLabel(),
																TextInput::make('margin_right')->label('Right')->integer()->inlineLabel(),
															]),
														]),
												Toggle::make('showFooter')
													->inline()
													->inlineLabel()
													->reactive()
													->label('Show Footer')
													->helperText('Footer will be shown at the bottom of the page.'),

												Group::make()
													->columns(6)
													->schema([
														Fieldset::make('Footer Settings')
															->columns(1)
															->columnStart(3)
															->columnSpan(4)
															->visible(fn ($get) => $get('showFooter'))
															->schema([

																Select::make('footerAlign')
																	->searchable()
																	->inlineLabel()
																	->label('Alignment')
																	->options([
																		'justify-start' => 'Left',
																		'justify-center' => 'Center',
																		'justify-end' => 'Right',
																	]),
																Select::make('footerFontFam')
																	->searchable()
																	->inlineLabel()
																	->label('Font Family')
																	->reactive()
																	->afterStateUpdated(fn ($set) => $set('footerDecor', null))
																	->options([
																		'Courier New' => 'Courier New',
																		'inherit' => 'inherit',
																	]),
																Select::make('footerDecor')
																	->searchable()
																	->inlineLabel()
																	->reactive()
																	->label('Decoration')
																	->visible(fn ($get) => $get('footerFontFam') === 'inherit')
																	->options([
																		'italic' => 'Italic',
																		'not-italic' => 'Reguler',
																	]),
																Select::make('footerFontWeight')
																	->searchable()
																	->inlineLabel()
																	->label('Font Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																Toggle::make('footerBorder')
																	->inlineLabel()
																	->label('Has Border'),

																TextInput::make('footerFontSize')
																	->inlineLabel()
																	->integer()
																	->suffix('px'),

																TextInput::make('footerContent')
																	->inlineLabel()
																	->placeholder('create new or pick one from the drop down')
																	->datalist([
																		'Thank you for your business. Please contact us if you have any questions about this invoice.',
																		'Thank you for your business. Please contact us if you have any questions regarding this invoice.',
																		'This invoice was generated electronically and does not require a physical signature.',
																		'We appreciate your prompt payment.',
																		'If you have already made this payment, please disregard this invoice.',
																		'Payment is due within the terms specified above.',
																		'Kindly reach out to our billing department for any clarifications or adjustments.',
																		'All amounts are in USD unless otherwise specified.',
																		'This invoice serves as a valid tax document and should be retained for your records.',
																		'Please include the invoice number in your payment reference.',
																		'Thank you for choosing our services. We look forward to working with you again.',
																		'This is a digitally generated invoice, no authorization signature is required',
																	]),
															]),
													])
											]),
										Group::make()
											->statePath('layout_config')
											->columns(2)
											->extraAttributes(['class' => 'mt-6'])
											->schema([
												Section::make('Letterhead Layout')
													->columns(2)
													->reactive()
													->collapsible()
													->headerActions([
														self::getInvoicePreviewAction(),
													])
													->schema([
														Radio::make('headingStyle')
															->columnSpanFull()
															->reactive()
															->inlineLabel(false)
															->inline()
															->label('Layout')
															->afterStateUpdated(function ($set) {
																$set('showInvoiceNumber', null);
															})
															->options([
																'grid' => 'Grid',
																'stacked' => 'Stacked',
															])
															->descriptions([
																'grid' => 'Horizontally in multiple columns.',
																'stacked' => 'Single vertical column, one below the other.',
															]),

														Fieldset::make('Title Style')
															->columns(1)
															->columnSpan(1)
															->columnStart(1)
															->schema([
																ColorPicker::make('headingColor')
																	->inlineLabel()
																	->label('Title Color')
																	->reactive(),

																Select::make('headingSize')
																	->searchable()
																	->inlineLabel()
																	->reactive()
																	->label('Title Size')
																	->placeholder('Select Title Size')
																	->options([
																		'h1' => 'H1 (largest)',
																		'h2' => 'H2',
																		'h3' => 'H3',
																		'h4' => 'H4',
																		'h5' => 'H5',
																		'h6' => 'H6 (smallest)',
																	]),

																Radio::make('headingWeight')
																	->inlineLabel()
																	->inline()
																	->reactive()
																	->label('Title Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),
															]),

														Fieldset::make('Heading Options')
															->columns(3)
															->columnSpan(1)
															->schema([
																Group::make()
																	->columns(2)
																	->columnSpanFull()
																	->schema([
																		Toggle::make('showLogo')
																			->reactive()
																			->columnSpan(1)
																			->label('Show Logo'),

																		Group::make()
																			// ->visible(fn ($get) => $get('showLogo') && $get('headingStyle') === 'grid')
																			->columnSpan(1)
																			->schema([
																				Select::make('logoPosition')
																					->searchable()
																					->reactive()
																					->inlineLabel()
																					->placeholder('select logo position')
																					->label('Position')
																					->visible(fn ($get) => $get('showLogo') && $get('headingStyle') === 'grid')
																					->afterStateUpdated(function ($set) {
																						$set('showInvoiceNumber', null);
																					})
																					->options([
																						'logo-left' => 'Left',
																						'logo-right' => 'Right',
																					]),
																				Select::make('headingVerticalAlign')
																					->searchable()
																					->inlineLabel()
																					->reactive()
																					->afterStateUpdated(function ($set) {
																						$set('showInvoiceNumber', null);
																					})
																					->label('Vertical')
																					->placeholder('select alignment')
																					->options([
																						'items-start' => 'Top',
																						'items-center' => 'Middle',
																						'items-end' => 'Bottom',
																					]),

																				Select::make('headingHorizontalAlign')
																					->searchable()
																					->inlineLabel()
																					->reactive()
																					->label('Horizontal')
																					->placeholder('select alignment')
																					->afterStateUpdated(function ($set, $state) {
																						$set('addressPosition', null);
																						$set('showInvoiceNumber', null);
																						if ($state !== 'text-end') {
																							$set('addressPosition', 'below');
																						}
																					})
																					->options([
																						'text-start' => 'Left',
																						'text-center' => 'Center',
																						'text-end' => 'Right',
																					]),
																				]),
																	]),
																Group::make()
																	->columns(2)
																	->columnSpanFull()
																	->schema([
																		Toggle::make('showAddress')
																			->reactive()
																			->columnSpan(1)
																			->label('Show Address')
																			->afterStateUpdated(function ($set) {
																				$set('showInvoiceNumber', null);
																				$set('addressPosition', null);
																			}),
																		Select::make('addressPosition')
																			->searchable()
																			->hiddenLabel()
																			->columnSpan(1)
																			->reactive()
																			->placeholder('select address position')
																			->label('Address Position')
																			->visible(fn ($get) =>
																				$get('headingStyle') === 'grid' &&
																				$get('showAddress') &&
																				$get('headingHorizontalAlign') === 'text-start'
																			)
																			->afterStateUpdated(function ($set) {
																				$set('showInvoiceNumber', null);
																			})
																			->options([
																				'below' => 'Below Company Name',
																				'far' => 'Far Right',
																			]),
																	]),
																Group::make()
																	->columns(2)
																	->columnSpanFull()
																	->schema([
																		Toggle::make('showSingleLineSpacer')
																			->reactive()
																			->columnSpan(1)
																			->label('Use Line Spacer'),
																		Toggle::make('showDoubleLineSpacer')
																			->reactive()
																			->columnSpan(1)
																			->visible(fn ($get) => $get('showSingleLineSpacer'))
																			->label('Use Double Line'),
																	]),
																Group::make()
																	->columnSpanFull()
																	->columns(2)
																	->schema([
																		Toggle::make('showInvoiceNumber')
																			->reactive()
																			->columnSpan(1)
																			->afterStateUpdated(function ($set) {
																				$set('invoiceTitleSize', null);
																				$set('titleOnly', null);
																				$set('invoiceTitlePosition', null);
																			})
																			->label('Show Invoice Title'),

																		Toggle::make('titleOnly')
																			->reactive()
																			->columnSpan(1)
																			->visible(fn ($get) => $get('showInvoiceNumber'))
																			->afterStateUpdated(function ($set) {
																				$set('invoiceTitleSize', null);
																				$set('invoiceTitlePosition', null);
																			})
																			->label('Show Title Only'),
																	])
															]),

														Fieldset::make('Invoice Title Style (Heading)')
															->columns(1)
															->columnSpan(1)
															->columnStart(2)
															->reactive()
															->visible(fn ($get) => $get('showInvoiceNumber'))
															->schema([
																Select::make('invoiceTitlePosition')
																	->label('Position')
																	->inlineLabel()
																	->reactive()
																	->options(function (callable $get) {
																		$options = [
																			'first' => 'First Line Body',
																		];

																		if ($get('headingStyle') === 'grid' &&
																			$get('showLogo') &&
																			$get('headingHorizontalAlign') !== 'text-end' &&
																			$get('logoPosition') === 'logo-left' &&
																			$get('addressPosition') !== 'far'
																		) {
																			$options['right'] = 'Most Right';
																		}

																		return $options;
																	}),

																Select::make('invoiceTitleSize')
																	->inlineLabel()
																	->reactive()
																	->label('Invoice Title Size')
																	->options([
																		'h3' => 'H3',
																		'h4' => 'H4',
																		'h5' => 'H5',
																		'h6' => 'H6 (smallest)',
																	]),

																Radio::make('invoiceTitleWeight')
																	->inlineLabel()
																	->inline()
																	->reactive()
																	->label('Title Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																Radio::make('invoiceNumberWeight')
																	->inlineLabel()
																	->inline()
																	->reactive()
																	->label('Invoice Number Weight')
																	->visible(fn ($get) => !$get('titleOnly'))
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),
															]),
													]),

												Section::make('Invoice Details Table Settings')
													->columns(2)
													->reactive()
													->collapsible()
													->headerActions([
														self::getInvoicePreviewAction(),
													])
													->schema([
														CheckboxList::make('tableVisibleColumns')
															->reactive()
															->inlineLabel()
															->options([
																'item' => 'Item',
																'qty' => 'Qty',
																'price' => 'Price',
															])
															->columns(4)
															->columnSpan(1)
															->afterStateHydrated(function ($component, $state) {
																// Jika belum ada nilai (misalnya saat create), tetapkan default
																if (! filled($state)) {
																	$component->state(['item', 'description', 'qty', 'price']);
																}
															}),
														Group::make()
															->columnSpan(1)
															->columns(1)
															->columnStart(1)
															->schema([
																Fieldset::make('Table Border Settings')
																	->columns(1)
																	->schema([
																		Select::make('tableBodyBorderStyle')
																			->label('Details Border Style')
																			->inlineLabel()
																			// ->inline()
																			->reactive()
																			->options([
																				'none' => 'None',
																				'row' => 'Row Only',
																				'column' => 'Column Only',
																				'full' => 'Full Border',
																			])
																			->helperText(fn ($state) =>
																				$state === 'none' ? 'No border will be applied.' :
																				($state === 'row' ? 'Applies horizontal borders only.' :
																				($state === 'column' ? 'Applies vertical borders only.' :
																				($state === 'full' ? 'Full border applies to all sides.' : null)))
																			)
																			->default('full'),

																		Select::make('footerBorderStyle')
																			->label('Summary Border Style')
																			->inlineLabel()
																			->reactive()
																			->options([
																				'none' => 'None',
																				'row' => 'Row Only',
																				'column' => 'Column Only',
																				'full' => 'Full Border',
																			])
																			->helperText(fn ($state) =>
																				$state === 'none' ? 'No border will be applied.' :
																				($state === 'row' ? 'Applies horizontal borders only.' :
																				($state === 'column' ? 'Applies vertical borders only.' :
																				($state === 'full' ? 'Full border applies to all sides.' : null)))
																			)
																			->default('full'),
																	]),

																Fieldset::make('Invoice Summary Settings')
																	->columns(1)
																	->schema([
																		Radio::make('tableSummaryOffGrid')
																			->label('Position')
																			->inline()
																			->inlineLabel()
																			->reactive()
																			->options([
																				'offgrid' => 'Off Grid',
																				'ongrid' => 'as Table Footer',
																			]),

																		Radio::make('tableSummaryAlignment')
																			->label('Alignment')
																			->inline()
																			->inlineLabel()
																			->options([
																				'left' => 'Left',
																				'right' => 'Right',
																			]),

																		ColorPicker::make('tableSummaryBg')
																			->label('Background Color')
																			->inlineLabel(),

																		Toggle::make('tableShowSummaryDetails')
																			->label('Additional Details')
																			->inline()
																			->inlineLabel(),

																		Toggle::make('tableShowInword')
																			->label('Include Amount inword')
																			->inline()
																			->reactive()
																			->inlineLabel(),
																	]),
															]),
														Group::make()
															->columnSpan(1)
															->schema([
																Fieldset::make('Color/Shading Settings')
																	->columns(1)
																	->columnSpan(1)
																	->schema([
																		ColorPicker::make('tableHeaderShading')
																			->label('Header Shading')
																			->extraAttributes(['class' => 'mb-6'])
																			->inlineLabel()
																			->reactive()
																			->default('#f3f4f6'),

																		Select::make('tableRowShading')
																			->label('Row Shading')
																			->inlineLabel()
																			->reactive()
																			->options([
																				'none' => 'None',
																				'zebra' => 'Zebra',
																			])
																			->default('zebra')
																			->helperText(fn ($state) =>
																				$state === 'none'
																					? 'No shading will be applied.'
																					: ($state === 'zebra'
																						? 'Applies zebra-style shading horizontally. You need to set a shading color.'
																						: null)
																			),

																		ColorPicker::make('tableRowColor')
																			->label('Row Color')
																			->inlineLabel()
																			->visible(fn ($get) => $get('tableRowShading') === 'zebra')
																			->reactive()
																			->default('#f3f4f6'),

																		Select::make('tableColumnShading')
																			->label('Column Shading')
																			->inlineLabel()
																			->reactive()
																			->options([
																				'none' => 'None',
																				'zebra' => 'Zebra',
																				'last' => 'Last Only',
																			])
																			->default('none')
																			->helperText(fn ($state) =>
																				$state === 'none' ? 'No shading will be applied' :
																				($state === 'zebra' ? 'Applies zebra-style shading vertically.' :
																				($state === 'last' ? 'Applies to last column only. you will need to set the shading color' : null))
																			),

																		ColorPicker::make('tableLastColumnColor')
																			->label('Column Color')
																			->inlineLabel()
																			->extraAttributes(['class' => 'mb-6'])
																			->default('#f3f4f6') // default bg-gray-200
																			->visible(fn ($get) => $get('tableColumnShading') === 'last')
																			->reactive()
																			->required(),
																	]),
															]),
													]),

												Section::make('Other Component Settings')
													->columns(2)
													->reactive()
													->collapsible()
													->headerActions([
														self::getInvoicePreviewAction(),
													])
													->schema([
														TableRepeater::make('repeater_layout')
															->hiddenLabel()
															->addActionLabel('Add Row')
															->extraAttributes(['class'=>'mb-6'])
															->columnSpanFull()
															// ->streamlined()
															->headers([
																Header::make('Row Position')->width('25%'),
																Header::make('Column Size')->width('25%'),
																Header::make('1st Column Content')->label('1st Column Content')->width('25%'),
																Header::make('2nd Column Content')->label('2nd Column Content')->width('25%'),
															])

															->schema([
																Select::make('row_position')
																	->searchable()
																	->columnSpan(1)
																	->placeholder('select position')
																	->options([
																		'before_table' => 'Before Table',
																		'after_table' => 'After Table',
																	]),
																Select::make('column_size')
																	->searchable()
																	->placeholder('select column size')
																	->afterStateUpdated(fn ($set) => $set('column_2_content', null))
																	->options([
																		1 => '1 Column',
																		2 => '2 Columns',
																	]),
																Select::make('column_1_content')
																	->searchable()
																	->placeholder('select content')
																	->multiple()
																	->options(function (callable $get) {
																		$hideAmountInword = $get('../../tableShowInword');
																		$summaryOffGrid = $get('../../tableSummaryOffGrid');
																		$rowPosition = $get('row_position');
																		return collect([
																			'amount_inword' => 'Amount Inword',
																			'bank_info' => 'Bank Info',
																			'bill_to' => 'Bill To',
																			'company_info' => 'Company Info',
																			'custom_input' => 'Custom Input',
																			'invoice_info' => 'Invoice Info',
																			'invoicesummary' => 'Invoice Summary',
																			'remark' => 'Remark',
																		])
																		->when($hideAmountInword, fn ($options) => $options->except('amount_inword'))
																		->unless($summaryOffGrid === 'offgrid' && $rowPosition === 'after_table', fn ($options) => $options->except('invoicesummary'))
																		->toArray();
																	}),
																Select::make('column_2_content')
																	->searchable()
																	->multiple()
																	->reactive()
																	->placeholder(fn ($get) => $get('column_size') == 1 ? 'disabled' : 'select content')
																	->disabled(fn ($get) => $get('column_size') == 1)
																	->options(function (callable $get) {
																		$hideAmountInword = $get('../../tableShowInword');
																		$summaryOffGrid = $get('../../tableSummaryOffGrid');
																		$rowPosition = $get('row_position');
																		return collect([
																			'amount_inword' => 'Amount Inword',
																			'bank_info' => 'Bank Info',
																			'bill_to' => 'Bill To',
																			'company_info' => 'Company Info',
																			'custom_input' => 'Custom Input',
																			'invoice_info' => 'Invoice Info',
																			'invoicesummary' => 'Invoice Summary',
																			'remark' => 'Remark',
																			'signature' => 'Signature',
																		])
																		->when($hideAmountInword, fn ($options) => $options->except('amount_inword'))
																		->unless($summaryOffGrid === 'offgrid' && $rowPosition === 'after_table', fn ($options) => $options->except('invoicesummary'))
																		->toArray();
																	}),
															]),

														Fieldset::make('Bill To Settings')
															->columns(1)
															->columnSpan(1)
															->visible(function ($get) {
																$items = $get('repeater_layout') ?? [];
																foreach ($items as $item) {
																	$col1 = (array) ($item['column_1_content'] ?? []);
																	$col2 = (array) ($item['column_2_content'] ?? []);

																	if (in_array('bill_to', $col1) || in_array('bill_to', $col2)) {
																		return true;
																	}
																}
																return false;
															})
															->schema([
																Radio::make('billtoContact')
																	->label('Contact Style')
																	->inlineLabel()
																	->options([
																		'inline' => 'Inline',
																		'stacked' => 'Stacked',
																	])
																	->descriptions([
																		'stacked' => 'Phone and email displayed as stacked, seperated from address line.',
																		'inline' => 'Phone and email are displayed inline with address line (default).'
																	])
															]),

														Fieldset::make('Invoice Information Settings (Body)')
															->columns(1)
															->columnSpan(1)
															->visible(function ($get) {
																$items = $get('repeater_layout') ?? [];
																foreach ($items as $item) {
																	$col1 = (array) ($item['column_1_content'] ?? []);
																	$col2 = (array) ($item['column_2_content'] ?? []);

																	if (in_array('invoice_info', $col1) || in_array('invoice_info', $col2)) {
																		return true;
																	}
																}
																return false;
															})
															->schema([
																Select::make('invoiceInfoLayout')
																	->inlineLabel()
																	->reactive()
																	->label('Layout')
																	->afterStateUpdated(function ($set) {
																		$set('invoiceHasBorder', null);
																	})
																	->options([
																		'flex' => 'Flex',
																		'grid' => 'Grid',
																		'stacked' => 'Stacked',
																	]),

																Toggle::make('invoiceHasBorder')
																	->inlineLabel()
																	->label('Has Border')
																	->reactive()
																	->visible(fn ($get) => $get('invoiceInfoLayout') !== 'stacked'),

																Select::make('invoiceInfoAlignment')
																	->label('Alignment')
																	->inlineLabel()
																	->reactive()
																	// ->visible(fn ($get) => $get('invoiceInfoLayout') !== 'stacked')
																	->options(fn ($get) => [
																		'justify-start' => 'Left',
																		'justify-center' => 'Center',
																		'justify-end' => 'Right',
																		...($get('invoiceInfoLayout') === 'flex'
																			? ['justify-between' => 'Between']
																			: []),
																	]),

																Select::make('invoiceInfoWeight')
																	->searchable()
																	->inlineLabel()
																	->label('Content Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																Select::make('invoiceDateStyle')
																	->searchable()
																	->inlineLabel()
																	->label('Date Style')
																	->options([
																		'j F Y'    => 'Long Month (e.g. 31 December 2025)',
																		'd M Y'    => 'Medium Month (e.g. 31 Dec 2025)',
																		'd-m-Y'    => 'Short (e.g. 31-12-2025)',
																	])
															]),

														Fieldset::make('Inword Settings')
															->columns(1)
															->columnSpan(1)
															->visible(function ($get) {
																$items = $get('repeater_layout') ?? [];
																foreach ($items as $item) {
																	$col1 = (array) ($item['column_1_content'] ?? []);
																	$col2 = (array) ($item['column_2_content'] ?? []);

																	if (in_array('amount_inword', $col1) || in_array('amount_inword', $col2)) {
																		return true;
																	}
																}
																return false;
															})
															->schema([
																Radio::make('inwordStyle')
																	->inlineLabel()
																	->inline()
																	->label('Text Decorative')
																	->options([
																		'italic' => 'Italic',
																		'not-italic' => 'Normal',
																	]),

																Radio::make('inwordWeight')
																	->inlineLabel()
																	->inline()
																	->label('Font Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																//when inword in single column only
																Radio::make('inwordAlign')
																	->inlineLabel()
																	->inline()
																	->label('Alignment')
																	->options([
																		'text-start' => 'Left',
																		'text-center' => 'Center',
																		'text-end' => 'Right',
																	]),

																Toggle::make('inwordBorder')
																	->inlineLabel()
																	->label('Has Border'),

																ColorPicker::make('inwordBg')
																	->inlineLabel()
																	->label('Background Color'),
															]),

														Fieldset::make('Bank Information Settings')
															->columns(1)
															->columnSpan(1)
															->visible(function ($get) {
																$items = $get('repeater_layout') ?? [];
																foreach ($items as $item) {
																	$col1 = (array) ($item['column_1_content'] ?? []);
																	$col2 = (array) ($item['column_2_content'] ?? []);

																	if (in_array('bank_info', $col1) || in_array('bank_info', $col2)) {
																		return true;
																	}
																}
																return false;
															})
															->schema([
																Radio::make('bankInfoLayout')
																	->inline()
																	->label('Layout')
																	->inlineLabel()
																	->columns(6)
																	->extraAttributes(['style' => 'gap: 0.5rem !important'])
																	->options([
																		'grid' => 'Grid',
																		'stacked' => 'Stacked',
																		'tabular' => 'Tabular',
																	]),

																Radio::make('bankInfoWeight')
																	->inlineLabel()
																	->inline()
																	->label('Font Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																Radio::make('bankInfoDecor')
																	->inlineLabel()
																	->inline()
																	->label('Decoration')
																	->options([
																		'italic' => 'Italic',
																		'not-italic' => 'Normal',
																	]),

																Toggle::make('bankInfoBorder')
																	->inlineLabel()
																	->label('Has Border'),

																ColorPicker::make('bankInfoBg')
																	->inlineLabel()
																	->label('Background Color'),
															]),

														Fieldset::make('Remark Settings')
															->columns(1)
															->columnSpan(1)
															->visible(function ($get) {
																$items = $get('repeater_layout') ?? [];
																foreach ($items as $item) {
																	$col1 = (array) ($item['column_1_content'] ?? []);
																	$col2 = (array) ($item['column_2_content'] ?? []);

																	if (in_array('remark', $col1) || in_array('remark', $col2)) {
																		return true;
																	}
																}
																return false;
															})
															->schema([
																Select::make('remarkStyle')
																	->searchable()
																	->inlineLabel()
																	->label('Text Decorative')
																	->options([
																		'italic' => 'Italic',
																		'not-italic' => 'Reguler',
																	]),

																Select::make('remarkWeight')
																	->searchable()
																	->inlineLabel()
																	->label('Font Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																Select::make('remarkAlign')
																	->searchable()
																	->inlineLabel()
																	->label('Alignment')
																	->options([
																		'justify-start' => 'Left',
																		'justify-center' => 'Center',
																		'justify-end' => 'Right',
																	]),

																Toggle::make('remarkBorder')
																	->inlineLabel()
																	->label('Has Border'),

																ColorPicker::make('remarkBg')
																	->inlineLabel()
																	->label('Background Color')
															]),

														Fieldset::make('Signature Settings')
															->columns(1)
															->columnSpan(1)
															->visible(function ($get) {
																$items = $get('repeater_layout') ?? [];
																foreach ($items as $item) {
																	$col1 = (array) ($item['column_1_content'] ?? []);
																	$col2 = (array) ($item['column_2_content'] ?? []);

																	if (in_array('signature', $col1) || in_array('signature', $col2)) {
																		return true;
																	}
																}
																return false;
															})
															->schema([
																Radio::make('signatureStyle')
																	->inline()
																	->inlineLabel()
																	->label('Style')
																	->options([
																		'style1' => 'Style 1',
																		'style2' => 'Style 2',
																		'style3' => 'Style 3',
																	]),

																Radio::make('signatureHorizontalAlign')
																	->inline()
																	->inlineLabel()
																	->label('Horizontal Align')
																	->options([
																		'items-start' => 'Left',
																		'items-center' => 'Center',
																		'items-end' => 'Right',
																	]),

																Radio::make('signatureFontWeight')
																	->inline()
																	->inlineLabel()
																	->label('Font Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																Radio::make('signatureNameUppercase')
																	->inline()
																	->inlineLabel()
																	->label('Text Uppercase')
																	->options([
																		'' => 'Normal',
																		'uppercase' => 'Uppercase',
																	]),

																TextInput::make('signatureHeight')
																	->label('Image Height')
																	->numeric()
																	->inlineLabel()
																	->inputMode('decimal'),


															]),

														Fieldset::make('Custom Input Settings')
															->columns(1)
															->columnSpan(1)
															->visible(function ($get) {
																$items = $get('repeater_layout') ?? [];
																foreach ($items as $item) {
																	$col1 = (array) ($item['column_1_content'] ?? []);
																	$col2 = (array) ($item['column_2_content'] ?? []);

																	if (in_array('custom_input', $col1) || in_array('custom_input', $col2)) {
																		return true;
																	}
																}
																return false;
															})
															->schema([
																Select::make('customInputAlignment')
																	->searchable()
																	->inlineLabel()
																	->label('Alignment')
																	->options([
																		'justify-start' => 'Left',
																		'justify-center' => 'Center',
																		'justify-end' => 'Right',
																	]),
																Select::make('customInputDecor')
																	->searchable()
																	->inlineLabel()
																	->reactive()
																	->label('Decoration')
																	->options([
																		'italic' => 'Italic',
																		'not-italic' => 'Reguler',
																	]),
																Select::make('customInputWeight')
																	->searchable()
																	->inlineLabel()
																	->label('Font Weight')
																	->options([
																		'font-normal' => 'Normal',
																		'font-medium' => 'Medium',
																		'font-bold' => 'Bold',
																	]),

																TextInput::make('customInputContent')
																	->inlineLabel()
																	->placeholder('create new or pick one from the drop down')
																	->datalist([
																		'Please make payment within 7 days of receiving this invoice.',
																		'Please contact us if you have any questions regarding this invoice.',
																	]),
															]),
													]),

											]),
									])
									->afterStateUpdated(function ($get){
										// Log::info($get('layout_config'));
									}),
							]),

						Section::make('Legacy Invoice Template')
							->columnSpanFull()
							->collapsible()
							->reactive()
							->visible(fn ($get) => $get('template_version') === 'v1')
							->columns(2)
							->schema([
								Fieldset::make('Layout')
									->columnSpan(1)
									->columns(2)
									->schema([
										Select::make('template')
											->searchable()
											->inlineLabel()
											->label('Main Layout')
											->columnSpan(2)
											->default('dynamic')
											->options(
												collect(File::files(resource_path('views/invoice')))
													->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
													->mapWithKeys(fn ($file) => [
														str_replace('.blade.php', '', $file->getFilename()) =>
														ucfirst(str_replace('.blade.php', '', $file->getFilename())),
													])
													->toArray()
											),
										Select::make('templateheading')
											->searchable()
											->inlineLabel()
											->columnSpan(2)
											->label('Heading Layout')
											->options(
												collect(File::files(resource_path('views/template/kop')))
													->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
													->mapWithKeys(fn ($file) => [
														str_replace('.blade.php', '', $file->getFilename()) =>
														ucfirst(str_replace('.blade.php', '', $file->getFilename())),
													])
													->toArray()
											),
										Select::make('templatebillto')
											->searchable()
											->inlineLabel()
											->columnSpan(2)
											->label('Billto Layout')
											->options(
												collect(File::files(resource_path('views/template/billto')))
													->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
													->mapWithKeys(fn ($file) => [
														str_replace('.blade.php', '', $file->getFilename()) =>
														ucfirst(str_replace('.blade.php', '', $file->getFilename())),
													])
													->toArray()
											),
										Select::make('templatetable')
											->searchable()
											->inlineLabel()
											->columnSpan(2)
											->label('Table Layout')
											->options(
												collect(File::files(resource_path('views/template/table')))
													->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
													->mapWithKeys(fn ($file) => [
														str_replace('.blade.php', '', $file->getFilename()) =>
														ucfirst(str_replace('.blade.php', '', $file->getFilename())),
													])
													->toArray()
											),
										Select::make('templateinword')
											->searchable()
											->inlineLabel()
											->columnSpan(2)
											->label('Words Layout')
											->options(
												collect(File::files(resource_path('views/template/inwords')))
													->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
													->mapWithKeys(fn ($file) => [
														str_replace('.blade.php', '', $file->getFilename()) =>
														ucfirst(str_replace('.blade.php', '', $file->getFilename())),
													])
													->toArray()
											),
										Select::make('templatebankinfo')
											->searchable()
											->inlineLabel()
											->columnSpan(2)
											->label('Bank Info Layout')
											->options(
												collect(File::files(resource_path('views/template/bankinfo')))
													->filter(fn ($file) => $file->getExtension() === 'php') // Hanya file .php
													->mapWithKeys(fn ($file) => [
														str_replace('.blade.php', '', $file->getFilename()) =>
														ucfirst(str_replace('.blade.php', '', $file->getFilename())),
													])
													->toArray()
											),
									]),

								Fieldset::make('Style')
									->columnSpan(1)
									->columns(1)
									->schema([
										Select::make('heading_size')
											->searchable()
											->inlineLabel()
											->label('Header Size')
											->options([
												'h1' => 'H1 (largest)',
												'h2' => 'H2',
												'h3' => 'H3',
												'h4' => 'H4',
												'h5' => 'H5',
												'h6' => 'H6 (smallest)',
											]),

										ColorPicker::make('text_color')
											->inlineLabel()
											->label('Page Heading Color'),
										ColorPicker::make('bg_color')
											->inlineLabel()
											->label('Header Footer table Color'),
										ColorPicker::make('footer_color')
											->inlineLabel()
											->label('Table Text Color'),
									]),
							]),
					])

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            BanksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'view' => Pages\ViewCompany::route('/{record}'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
            'transactions' => Pages\CompanyTransactionDetails::route('/{record}/transaction-details'),
            // 'invoiceEditor' => Pages\EditCompany::route('/{record}/edit'),
        ];
    }

	protected static function getInvoicePreviewAction(): Action
	{
		return Action::make('preview')
			->label('Invoice Preview')
			->icon('heroicon-o-eye')
			->color('success')
			->tooltip('Preview Invoice')
			->modalHeading('Preview Invoice')
			->modalSubmitAction(false)
			->modalCancelActionLabel('Close')
			->modalWidth('6xl')
			->modalAlignment(Alignment::Right)
			->modalContent(function ($get, $record) {
				// heading - handle null record for new company creation
				$companyName = $get('../../name') ?? 'Sample Company Name';
				$companyAddress = $get('../../address') ?? '<p>123 Sample Street<br>Sample City, Sample State 12345</p>';
				$companyLogo = $record?->logo ?? 'logo/01JDSR8G4MGGJ3AZ8RZJQ8SHBW.png';
				$companyPhone = $get('../../phone') ?? '+****************';
				$companyEmail = $get('../../email') ?? '<EMAIL>';
				$companySignature = $record?->signature ?? 'signature.jpeg';
				$companySignatureName = $get('../../signature_name') ?? 'John Doe';
				$companyType = $get('../../type') ?? 1;

				// Company type and currency logic
				$isIndonesianCompany = $companyType == 2;
				$currencySymbol = $isIndonesianCompany ? 'Rp.' : '$';
				$currencyCode = $isIndonesianCompany ? 'IDR' : 'USD';

				// Multiple banks data - use dummy data for preview
				$companyBanks = [];
				if ($record && method_exists($record, 'banks') && $record->banks) {
					// Use real data if available
					try {
						$companyBanks = $record->banks()
							->where('include_in_invoice', true)
							->get()
							->toArray();
					} catch (\Exception $e) {
						// Fallback to empty if relationship fails
						$companyBanks = [];
					}
				}

				// If no real banks or creating new company, use dummy data
				if (empty($companyBanks)) {
					$companyBanks = [
						[
							'id' => 1,
							'bank_name' => $isIndonesianCompany ? 'Bank Central Asia' : 'Chase Bank',
							'bank_acc_name' => $companyName ?: 'Sample Company Name',
							'bank_acc_no' => $isIndonesianCompany ? '**********' : '**********12',
							'swift' => $isIndonesianCompany ? 'CENAIDJA' : 'CHASUS33',
							'bank_address' => $isIndonesianCompany ?
								'<p>Menara BCA, Jl. M.H. Thamrin No.1<br>Jakarta Pusat 10310, Indonesia</p>' :
								'<p>270 Park Avenue<br>New York, NY 10017, USA</p>',
							'bank_code' => $isIndonesianCompany ? '014' : '*********',
							'routing_no' => $isIndonesianCompany ? null : '*********',
							'iban' => $isIndonesianCompany ? null : '**********************',
							'custom_columns' => [
								'branch_location' => [
									'type' => 'text',
									'value' => $isIndonesianCompany ? 'Jakarta Pusat' : 'Manhattan'
								],
								'contact_person' => [
									'type' => 'text',
									'value' => $isIndonesianCompany ? 'Budi Santoso' : 'John Smith'
								]
							]
						],
						[
							'id' => 2,
							'bank_name' => $isIndonesianCompany ? 'Bank Mandiri' : 'Bank of America',
							'bank_acc_name' => $companyName ?: 'Sample Company Name',
							'bank_acc_no' => $isIndonesianCompany ? '**********' : '**********98',
							'swift' => $isIndonesianCompany ? 'BMRIIDJA' : 'BOFAUS3N',
							'bank_address' => $isIndonesianCompany ?
								'<p>Plaza Mandiri, Jl. Jend. Gatot Subroto<br>Jakarta Selatan 12930, Indonesia</p>' :
								'<p>100 N Tryon St<br>Charlotte, NC 28255, USA</p>',
							'bank_code' => $isIndonesianCompany ? '008' : '*********',
							'routing_no' => $isIndonesianCompany ? null : '*********',
							'iban' => $isIndonesianCompany ? null : '**********************',
							'custom_columns' => [
								'branch_location' => [
									'type' => 'text',
									'value' => $isIndonesianCompany ? 'Jakarta Selatan' : 'Charlotte'
								]
							]
						]
					];
				}
				$headingStyle = $get('headingStyle') ?? 'stacked';
				$headingColor = $get('headingColor') ?? '#000000';
				$headingSize = $get('headingSize') ?? 'h3';
				$headingWeight = $get('headingWeight') ?? 'font-normal';
				$headingVerticalAlign = $get('headingVerticalAlign') ?? 'items-center';
				$headingHorizontalAlign = $get('headingHorizontalAlign') ?? 'text-start';
				$showLogo = $get('showLogo') ?? false;
				$showAddress = $get('showAddress') ?? false;
				$addressPosition = $get('addressPosition') ?? 'near';
				$logoPosition = $get('logoPosition') ?? 'logo-left';
				$showInvoiceNumber = $get('showInvoiceNumber') ?? false;
				$invoiceTitlePosition = $get('invoiceTitlePosition') ?? '';
				$invoiceTitleSize = $get('invoiceTitleSize') ?? 'h6';
				$invoiceTitleWeight = $get('invoiceTitleWeight') ?? 'font-normal';
				$invoiceNumberWeight = $get('invoiceNumberWeight') ?? 'font-normal';

				$showSingleLineSpacer = $get('showSingleLineSpacer') ?? false;
				$showDoubleLineSpacer = $get('showDoubleLineSpacer') ?? false;

				//body
				//bank info
				$bankInfoLayout = $get('bankInfoLayout') ?? 'stacked';
				$bankInfoBorder = $get('bankInfoBorder') ?? '';
				$bankInfoWeight = $get('bankInfoWeight') ?? 'font-normal';
				$bankInfoBg = $get('bankInfoBg') ?? 'ffffff';
				$bankInfoDecor = $get('bankInfoDecor') ?? 'not-italic';

				//billto style
				$billtoContact = $get('billtoContact') ?? 'inline';

				$customInputAlignment = $get('customInputAlignment') ?? 'justify-start';
				$customInputDecor = $get('customInputDecor') ?? 'not-italic';
				$customInputWeight = $get('customInputWeight') ?? 'font-normal';
				$customInputContent = $get('customInputContent') ?? '';

				//invoice info
				$invoiceInfoLayout = $get('invoiceInfoLayout') ?? 'stacked';
				$isBordered = $get('invoiceHasBorder') ?? false;
				$invoiceInfoWeight = $get('invoiceInfoWeight') ?? 'font-normal';
				$titleOnly = $get('titleOnly') ?? false;
				$invoiceInfoAlignment = $get('invoiceInfoAlignment') ?? 'text-start';
				$invoiceDateStyle = $get('invoiceDateStyle') ?? 'j F Y';

				//amount inword
				$inwordStyle = $get('inwordStyle') ?? 'not-italic';
				$inwordWeight = $get('inwordWeight') ?? 'font-normal';
				$inwordAlign = $get('inwordAlign') ?? 'text-start';
				$inwordBorder = $get('inwordBorder') ?? false;
				$inwordBg = $get('inwordBg') ?? '#ffffff';

				//remark
				$remarkStyle = $get('remarkStyle') ?? 'not-italic';
				$remarkWeight = $get('remarkWeight') ?? 'font-normal';
				$remarkAlign = $get('remarkAlign') ?? 'text-start';
				$remarkBorder = $get('remarkBorder') ?? false;
				$remarkBg = $get('remarkBg') ?? 'ffffff';

				//signature
				$signatureStyle = $get('signatureStyle') ?? 'style1';
				$signatureHeight = $get('signatureHeight') ?? '5';
				$signatureHorizontalAlign = $get('signatureHorizontalAlign') ?? 'items-start';
				$signatureFontWeight = $get('signatureFontWeight') ?? 'font-medium';
				$signatureNameUppercase = $get('signatureNameUppercase') ?? '';

				//table
				$visibleColumns = $get('tableVisibleColumns') ?? ['item', 'description', 'qty', 'price'];
				$columnShading = $get('tableColumnShading') ?? 'none';
				$headerShading = $get('tableHeaderShading') ?? '#f3f4f6';
				$rowShading = $get('tableRowShading') ?? 'none';
				$rowColor = $get('tableRowColor') ?? '#f3f4f6';
				$tableLastColumnColor = $get('tableLastColumnColor') ?? '#f3f4f6';
				$bodyBorderStyle = $get('tableBodyBorderStyle') ?? 'full';
				$footerBorderStyle = $get('footerBorderStyle') ?? 'full';
				$tableSummaryOffGrid = $get('tableSummaryOffGrid') ?? 'ongrid';
				$tableShowSummaryDetails = $get('tableShowSummaryDetails');
				$tableSummaryBg = $get('tableSummaryBg') ?? '#ffffff';
				$tableShowInword = $get('tableShowInword') ?? false;
				$tableSummaryAlignment = $get('tableSummaryAlignment') ?? 'left';

				//footer
				$showFooter = $get('showFooter') ?? false;
				$footerDecor = $get('footerDecor') ?? 'none';
				$footerAlign = $get('footerAlign') ?? 'justify-start';
				$footerFontFam = $get('footerFontFam') ?? 'inherit';
				$footerFontSize = $get('footerFontSize') ?? '10';
				$footerFontWeight = $get('footerFontWeight') ?? 'font-normal';
				$footerContent = $get('footerContent') ?? null;
				$footerBorder = $get('footerBorder') ?? 'full';

				$repeaterLayout = $get('repeater_layout') ?? [];

				$viewPath = "template.v2.invoicePreview";
				// Log::info($companyLogo);
				// Log::info($companyName);
				if (!view()->exists($viewPath)) {
					return new HtmlString('<div class="text-red-500">Preview tidak tersedia</div>');
				}

				// Kirim data warna ke view
				return new HtmlString(view($viewPath, [
					//parent
					'record' => $record,
					'companyName' => $companyName,
					'companyAddress' => $companyAddress,
					'companyLogo' => $companyLogo,
					'companyPhone' => $companyPhone,
					'companyEmail' => $companyEmail,
					'companySignature' => $companySignature,
					'companySignatureName' => $companySignatureName,
					'companyType' => $companyType,
					'isIndonesianCompany' => $isIndonesianCompany,
					'currencySymbol' => $currencySymbol,
					'currencyCode' => $currencyCode,
					'companyBanks' => $companyBanks,

					//heading
					'headingStyle' => $headingStyle,
					'headingColor' => $headingColor,
					'headingSize' => $headingSize,
					'headingWeight' => $headingWeight,
					'headingVerticalAlign' => $headingVerticalAlign,
					'headingHorizontalAlign' => $headingHorizontalAlign,
					'headingShowLogo' => $showLogo,
					'headingShowAddress' => $showAddress,
					'headingAddressPosition' => $addressPosition,
					'headingLogoPosition' => $logoPosition,
					'headingShowInvoiceNumber' => $showInvoiceNumber,
					'headingInvoiceTitlePosition' => $invoiceTitlePosition,
					'headingInvoiceTitleSize' => $invoiceTitleSize,
					'headingInvoiceTitleWeight' => $invoiceTitleWeight,
					'headingInvoiceNumberWeight' => $invoiceNumberWeight,
					'headingInvoiceTitleOnly' => $titleOnly,

					'headingSingleLineSpacer' => $showSingleLineSpacer,
					'headingDoubleLineSpacer' => $showDoubleLineSpacer,

					//body
					'repeaterLayout' => $repeaterLayout,

					//bankinfo
					'bankInfoLayout' => $bankInfoLayout,
					'bankInfoBorder' => $bankInfoBorder,
					'bankInfoWeight' => $bankInfoWeight,
					'bankInfoBg' => $bankInfoBg,
					'bankInfoDecor' => $bankInfoDecor,

					//billto style
					'billToContact' => $billtoContact,

					//custom input
					'customInputAlignment' => $customInputAlignment,
					'customInputDecor' => $customInputDecor,
					'customInputWeight' => $customInputWeight,
					'customInputContent' => $customInputContent,

					//invoice info
					'invoiceInfoLayout' => $invoiceInfoLayout,
					'invoiceInfoIsBordered' => $isBordered,
					'invoiceInfoWeight' => $invoiceInfoWeight,
					'invoiceInfoAlignment' => $invoiceInfoAlignment,
					'invoiceDateStyle' => $invoiceDateStyle,

					//amount inword
					'inwordStyle'=>$inwordStyle,
					'inwordWeight'=>$inwordWeight,
					'inwordAlign'=>$inwordAlign,
					'inwordBorder'=>$inwordBorder,
					'inwordBg'=>$inwordBg,

					//remark
					'remarkDecorative' => $remarkStyle,
					'remarkWeight' => $remarkWeight,
					'remarkAlign' => $remarkAlign,
					'remarkBorder' => $remarkBorder,
					'remarkBg' => $remarkBg,

					//signature
					'signatureStyle' => $signatureStyle,
					'signatureHeight' => $signatureHeight,
					'signatureHorizontalAlign' => $signatureHorizontalAlign,
					'signatureFontWeight' => $signatureFontWeight,
					'signatureNameUppercase' => $signatureNameUppercase,

					//table
					'tableVisibleColumns' => $visibleColumns,
					'tableColumnShading' => $columnShading,
					'tableLastColumnColor' => $tableLastColumnColor,
					'tableHeaderShading' => $headerShading,
					'tableRowShading' => $rowShading,
					'tableRowColor' => $rowColor,
					'tableBodyBorderStyle' => $bodyBorderStyle,
					'footerBorderStyle' => $footerBorderStyle,
					'tableSummaryOffGrid' => $tableSummaryOffGrid,
					'tableShowSummaryDetails' => $tableShowSummaryDetails,
					'tableSummaryBg' => $tableSummaryBg,
					'tableShowInword' => $tableShowInword,
					'tableSummaryAlignment' => $tableSummaryAlignment,

					//footer
					'showFooter' => $showFooter,
					'footerDecor' => $footerDecor,
					'footerBorder' => $footerBorder,
					'footerAlign' => $footerAlign,
					'footerFontFam' => $footerFontFam,
					'footerFontSize' => $footerFontSize,
					'footerFontWeight' => $footerFontWeight,
					'footerContent' => $footerContent,

				])->render());
			});
	}
}
